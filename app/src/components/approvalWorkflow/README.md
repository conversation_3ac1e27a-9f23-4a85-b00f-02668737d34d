# Approval Workflow Components

This directory contains the components for the Approval Workflow Setup feature in the avinya-ops application.

## Components Overview

### 1. ApprovalWorkflowForm.tsx
The main form component that orchestrates the entire workflow configuration process.

**Features:**
- Basic workflow information (name, form type)
- Criteria configuration section
- Approval levels configuration
- Auto-approve/auto-reject options
- Real-time workflow preview
- Form validation and error handling

### 2. CriteriaBuilder.tsx
Dynamic criteria builder component for defining workflow trigger conditions.

**Features:**
- Add/remove criteria rows dynamically
- Field type selection (Department, Designation, Location, Employee)
- Operator selection (IS, IS NOT)
- Value selection with filtered dropdowns
- Logic connectors (AND, OR) between criteria
- Real-time criteria preview
- Criteria summary display

### 3. WorkflowLevels.tsx
Component for configuring approval hierarchy levels.

**Features:**
- Radio button selection for approval levels (1-4)
- Visual approval path preview
- Level descriptions and explanations
- Information about how approval levels work

### 4. WorkflowPreview.tsx
Preview component that shows how the configured workflow will operate.

**Features:**
- Workflow status badge
- Trigger conditions display
- Approval flow visualization
- Workflow summary with key metrics
- Expected duration calculation

## Data Structure

### Mock Data
- **mockDepartments**: Sample department options
- **mockDesignations**: Sample designation options
- **mockLocations**: Sample location options
- **mockEmployees**: Sample employee options
- **mockFormTypes**: Available form types for workflows

### Types
- **WorkflowCriteria**: Individual criteria condition
- **ApprovalWorkflow**: Complete workflow configuration
- **CriteriaFieldType**: Field types for criteria
- **CriteriaOperator**: Operators for conditions
- **LogicConnector**: Logic connectors between criteria

## Usage

```tsx
import ApprovalWorkflowForm from '@/components/approvalWorkflow/ApprovalWorkflowForm';

// In your component
<ApprovalWorkflowForm
  workflow={selectedWorkflow} // Optional: for editing existing workflow
  onSubmit={handleWorkflowSubmit}
  onCancel={handleCancel}
  loading={isLoading}
/>
```

## Styling

Each component has its own CSS file following the existing application patterns:
- Uses CSS custom properties for theming
- Responsive design with mobile-first approach
- Consistent with PrimeReact component styling
- Follows avinya-ops design system

## Features Implemented

✅ Dynamic criteria builder with multiple conditions
✅ Approval level configuration (1-4 levels)
✅ Auto-approve/auto-reject options
✅ Real-time workflow preview
✅ Form validation and error handling
✅ Responsive design
✅ Mock data integration
✅ TypeScript support
✅ Consistent UI/UX with existing app

## Future Enhancements

- Backend API integration
- Advanced criteria operators (CONTAINS, GREATER_THAN, etc.)
- Custom approval hierarchy configuration
- Workflow testing and simulation
- Workflow analytics and reporting
- Email notification templates
- Conditional approval paths
