/**
 * Mock data for approval workflow setup
 */

import {
  DepartmentOption,
  DesignationOption,
  LocationOption,
  EmployeeOption,
  FormTypeOption,
  ApprovalWorkflow,
  CriteriaFieldType,
  CriteriaOperator,
  LogicConnector
} from '@/types/approvalWorkflow.types';

/**
 * Mock departments data
 */
export const mockDepartments: DepartmentOption[] = [
  {
    id: 'dept-001',
    label: 'Engineering',
    value: 'engineering',
    description: 'Software development and technical operations'
  },
  {
    id: 'dept-002',
    label: 'Human Resources',
    value: 'human-resources',
    description: 'Employee management and organizational development'
  },
  {
    id: 'dept-003',
    label: 'Finance',
    value: 'finance',
    description: 'Financial planning and accounting'
  },
  {
    id: 'dept-004',
    label: 'Marketing',
    value: 'marketing',
    description: 'Brand promotion and customer engagement'
  },
  {
    id: 'dept-005',
    label: 'Operations',
    value: 'operations',
    description: 'Business operations and process management'
  },
  {
    id: 'dept-006',
    label: 'Sales',
    value: 'sales',
    description: 'Revenue generation and client relations'
  }
];

/**
 * <PERSON>ck designations data
 */
export const mockDesignations: DesignationOption[] = [
  {
    id: 'desig-001',
    label: 'Software Engineer',
    value: 'software-engineer',
    description: '<PERSON>elops and maintains software applications'
  },
  {
    id: 'desig-002',
    label: 'Senior Software Engineer',
    value: 'senior-software-engineer',
    description: 'Senior-level software development role'
  },
  {
    id: 'desig-003',
    label: 'Team Lead',
    value: 'team-lead',
    description: 'Leads a team of developers'
  },
  {
    id: 'desig-004',
    label: 'Project Manager',
    value: 'project-manager',
    description: 'Manages projects and coordinates teams'
  },
  {
    id: 'desig-005',
    label: 'HR Manager',
    value: 'hr-manager',
    description: 'Manages human resources operations'
  },
  {
    id: 'desig-006',
    label: 'Finance Manager',
    value: 'finance-manager',
    description: 'Oversees financial operations'
  },
  {
    id: 'desig-007',
    label: 'Marketing Specialist',
    value: 'marketing-specialist',
    description: 'Handles marketing campaigns and strategies'
  },
  {
    id: 'desig-008',
    label: 'Operations Manager',
    value: 'operations-manager',
    description: 'Manages day-to-day operations'
  }
];

/**
 * Mock locations data
 */
export const mockLocations: LocationOption[] = [
  {
    id: 'loc-001',
    label: 'New York Office',
    value: 'new-york',
    city: 'New York',
    state: 'NY'
  },
  {
    id: 'loc-002',
    label: 'San Francisco Office',
    value: 'san-francisco',
    city: 'San Francisco',
    state: 'CA'
  },
  {
    id: 'loc-003',
    label: 'Chicago Office',
    value: 'chicago',
    city: 'Chicago',
    state: 'IL'
  },
  {
    id: 'loc-004',
    label: 'Austin Office',
    value: 'austin',
    city: 'Austin',
    state: 'TX'
  },
  {
    id: 'loc-005',
    label: 'Remote',
    value: 'remote',
    city: 'Remote',
    state: 'N/A'
  }
];

/**
 * Mock employees data
 */
export const mockEmployees: EmployeeOption[] = [
  {
    id: 'emp-001',
    label: 'John Doe',
    value: 'john-doe',
    email: '<EMAIL>',
    department: 'Engineering',
    designation: 'Senior Software Engineer'
  },
  {
    id: 'emp-002',
    label: 'Jane Smith',
    value: 'jane-smith',
    email: '<EMAIL>',
    department: 'Human Resources',
    designation: 'HR Manager'
  },
  {
    id: 'emp-003',
    label: 'Robert Johnson',
    value: 'robert-johnson',
    email: '<EMAIL>',
    department: 'Finance',
    designation: 'Finance Manager'
  },
  {
    id: 'emp-004',
    label: 'Emily Davis',
    value: 'emily-davis',
    email: '<EMAIL>',
    department: 'Marketing',
    designation: 'Marketing Specialist'
  },
  {
    id: 'emp-005',
    label: 'Michael Wilson',
    value: 'michael-wilson',
    email: '<EMAIL>',
    department: 'Operations',
    designation: 'Operations Manager'
  },
  {
    id: 'emp-006',
    label: 'Sarah Brown',
    value: 'sarah-brown',
    email: '<EMAIL>',
    department: 'Engineering',
    designation: 'Team Lead'
  }
];

/**
 * Mock form types data
 */
export const mockFormTypes: FormTypeOption[] = [
  {
    label: 'Purchase Request',
    value: 'purchase-request',
    description: 'Requests for purchasing goods or services'
  },
  {
    label: 'Expense Report',
    value: 'expense-report',
    description: 'Employee expense reimbursement requests'
  },
  {
    label: 'Leave Request',
    value: 'leave-request',
    description: 'Employee time-off requests'
  },
  {
    label: 'Budget Approval',
    value: 'budget-approval',
    description: 'Budget allocation and approval requests'
  },
  {
    label: 'Vendor Contract',
    value: 'vendor-contract',
    description: 'Vendor agreement and contract approvals'
  }
];

/**
 * Field type options for criteria builder
 */
export const fieldTypeOptions = [
  { label: 'Department', value: 'department' as CriteriaFieldType },
  { label: 'Designation', value: 'designation' as CriteriaFieldType },
  { label: 'Location', value: 'location' as CriteriaFieldType },
  { label: 'Employee', value: 'employee' as CriteriaFieldType }
];

/**
 * Operator options for criteria
 */
export const operatorOptions = [
  { label: 'IS', value: 'IS' as CriteriaOperator },
  { label: 'IS NOT', value: 'IS_NOT' as CriteriaOperator }
];

/**
 * Logic connector options
 */
export const logicConnectorOptions = [
  { label: 'AND', value: 'AND' as LogicConnector },
  { label: 'OR', value: 'OR' as LogicConnector }
];

/**
 * Approval level options
 */
export const approvalLevelOptions = [
  { label: 'Level 1 (Direct Manager)', value: 1 },
  { label: 'Level 2 (Manager + Their Manager)', value: 2 },
  { label: 'Level 3 (3-Level Hierarchy)', value: 3 },
  { label: 'Level 4 (4-Level Hierarchy)', value: 4 }
];

/**
 * Mock existing workflows for the list view
 */
export const mockWorkflows: ApprovalWorkflow[] = [
  {
    id: 'workflow-001',
    name: 'Engineering Purchase Requests',
    formType: 'purchase-request',
    criteria: [
      {
        id: 'criteria-001',
        fieldType: 'department',
        operator: 'IS',
        value: 'engineering',
        logicConnector: 'AND'
      },
      {
        id: 'criteria-002',
        fieldType: 'designation',
        operator: 'IS_NOT',
        value: 'team-lead'
      }
    ],
    approvalLevels: 2,
    autoApprove: false,
    autoReject: false,
    isActive: true,
    createdDate: '2024-01-15',
    lastModifiedDate: '2024-01-20'
  },
  {
    id: 'workflow-002',
    name: 'HR Leave Requests',
    formType: 'leave-request',
    criteria: [
      {
        id: 'criteria-003',
        fieldType: 'department',
        operator: 'IS',
        value: 'human-resources'
      }
    ],
    approvalLevels: 1,
    autoApprove: false,
    autoReject: false,
    isActive: true,
    createdDate: '2024-01-10',
    lastModifiedDate: '2024-01-10'
  }
];
