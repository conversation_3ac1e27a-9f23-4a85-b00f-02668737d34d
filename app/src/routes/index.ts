import { loginRoute } from "@/routes/public/login.route";
import { indexRoute } from "@/routes/public/index.route";
import { rootRoute } from "@/routes/root.route";
import { appRoute } from "@/routes/private/app.route";
import { dashboardRoute } from "@/routes/private/dashboard.route";
import { organizationDetailsRoute } from "@/routes/private/organizationDetails.route";
import { notFoundRoute } from "@/routes/common/notFound.route";
import { addEmployeeRoute } from "./private/addEmployee.route";
import { employeeDetailsRoute } from "./private/employeeDetails.route";
import { editEmployeeRoute } from "./private/editEmployee.route";
import { locationDetailsRoute } from "./private/locationDetails.route";
import { departmentDetailsRoute } from "./private/departmentDetails.route";
import { designationDetailsRoute } from "./private/designationDetails.route";
import { organizationItemCatalogRoute } from "./private/organizationItemCatalog.route";
import { addProductRoute } from "./private/addProduct.route";
import { editProductRoute } from "./private/editProduct.route";
import { addCategoryRoute } from "./private/addCategory.route";
import { editCategoryRoute } from "./private/editCategory.route";
import { addSubcategoryRoute } from "./private/addSubcategory.route";
import { editSubcategoryRoute } from "./private/editSubcategory.route";
import { approvalWorkflowsRoute } from "./private/approvalWorkflows.route";

export const routeTree = rootRoute.addChildren([
    indexRoute,
    loginRoute,
    appRoute.addChildren([
        dashboardRoute,
        organizationDetailsRoute,
        employeeDetailsRoute,
        locationDetailsRoute,
        departmentDetailsRoute,
        designationDetailsRoute,
        organizationItemCatalogRoute,
        addEmployeeRoute,
        editEmployeeRoute,
        addProductRoute,
        editProductRoute,
        addCategoryRoute,
        editCategoryRoute,
        addSubcategoryRoute,
        editSubcategoryRoute,
        approvalWorkflowsRoute,
    ]),
    notFoundRoute,
]);